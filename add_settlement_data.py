import pandas as pd
from sqlalchemy import create_engine, select, func, and_, or_
from sqlalchemy.orm import sessionmaker
from models import TblGeneration, TblConsumption, ConsumptionMapping, SettlementData, Base
from datetime import datetime, timedelta
import logging
from decimal import Decimal
from collections import defaultdict
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SettlementDataInserter:
    def __init__(self, database_url="mysql+pymysql://root:test123@localhost/energy_db", batch_size=1000):
        """
        Initialize the Settlement Data Inserter
        
        Args:
            database_url (str): Database connection URL
            batch_size (int): Number of records to process in each batch
        """
        self.engine = create_engine(database_url)
        self.Session = sessionmaker(bind=self.engine)
        self.batch_size = batch_size
        
    def process_settlement_data(self):
        """
        Optimized processing of settlement data using bulk operations and caching
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            start_time = time.time()
            logger.info("Starting optimized settlement data processing")
            
            session = self.Session()
            
            # Step 1: Load all required data upfront (bulk loading)
            logger.info("Loading data from database...")
            
            # Load all generation records
            generation_df = pd.read_sql(
                session.query(TblGeneration).statement,
                session.bind
            )
            logger.info(f"Loaded {len(generation_df)} generation records")
            
            # Load all consumption records
            consumption_df = pd.read_sql(
                session.query(TblConsumption).statement,
                session.bind
            )
            logger.info(f"Loaded {len(consumption_df)} consumption records")
            
            # Load all consumption mappings
            mapping_df = pd.read_sql(
                session.query(ConsumptionMapping).statement,
                session.bind
            )
            logger.info(f"Loaded {len(mapping_df)} consumption mappings")
            
            # Load existing settlement data for comparison
            existing_settlement_df = pd.read_sql(
                session.query(SettlementData).statement,
                session.bind
            )
            logger.info(f"Loaded {len(existing_settlement_df)} existing settlement records")
            
            # Step 2: Create efficient lookup structures
            logger.info("Creating lookup structures...")
            
            # Create mapping lookup: client_name -> list of mappings
            mapping_lookup = defaultdict(list)
            for _, mapping in mapping_df.iterrows():
                mapping_lookup[mapping['client_name']].append(mapping)
            
            # Create consumption lookup: (client_name, cons_unit, date, time) -> consumption
            consumption_lookup = {}
            for _, cons in consumption_df.iterrows():
                key = (cons['client_name'], cons['cons_unit'], cons['date'], cons['time'])
                consumption_lookup[key] = cons['consumption']
            
            # Create existing settlement lookup
            existing_settlement_lookup = {}
            for _, settlement in existing_settlement_df.iterrows():
                key = (settlement['plant_id'], settlement['client_name'], settlement['cons_unit'], 
                       settlement['date'], settlement['time'], settlement['type'])
                existing_settlement_lookup[key] = settlement
            
            # Create plant lookup: client_name -> (plant_id, type)
            plant_lookup = {}
            for _, gen in generation_df.iterrows():
                if gen['client_name'] not in plant_lookup:
                    plant_lookup[gen['client_name']] = (gen['plant_id'], gen['type'])
            
            # Step 3: Process generation records in batches
            logger.info("Processing generation records...")
            
            new_records = []
            updated_records = []
            records_added = 0
            records_updated = 0
            records_skipped = 0
            
            # Process generation records
            for idx, gen_record in generation_df.iterrows():
                try:
                    client_name = gen_record['client_name']
                    mappings = mapping_lookup.get(client_name, [])
                    
                    if not mappings:
                        logger.warning(f"No consumption mapping found for client: {client_name}")
                        records_skipped += 1
                        continue
                    
                    # Process each mapping for this generation record
                    for mapping in mappings:
                        # Calculate allocated generation
                        supplied = gen_record['supplied'] if pd.notna(gen_record['supplied']) else 0
                        allocated_generation = Decimal(supplied) * (Decimal(mapping['percentage']) / Decimal(100))
                        
                        # Find matching consumption
                        cons_key = (client_name, mapping['cons_unit'], gen_record['date'], gen_record['time'])
                        consumption_val = consumption_lookup.get(cons_key, 0)
                        consumption = Decimal(consumption_val) if consumption_val is not None else Decimal(0)
                        
                        # Calculate settlement values
                        deficit = allocated_generation - consumption
                        surplus_demand = max(Decimal(0), -deficit)
                        surplus_generation = max(Decimal(0), allocated_generation - consumption)
                        settled = min(allocated_generation, consumption)
                        
                        # Check if record exists
                        settlement_key = (gen_record['plant_id'], client_name, mapping['cons_unit'],
                                        gen_record['date'], gen_record['time'], gen_record['type'])
                        
                        settlement_data = {
                            'plant_id': gen_record['plant_id'],
                            'client_name': client_name,
                            'cons_unit': mapping['cons_unit'],
                            'type': gen_record['type'],
                            'date': gen_record['date'],
                            'time': gen_record['time'],
                            'allocated_generation': allocated_generation,
                            'consumption': consumption,
                            'deficit': deficit,
                            'surplus_demand': surplus_demand,
                            'surplus_generation': surplus_generation,
                            'settled': settled
                        }
                        
                        if settlement_key in existing_settlement_lookup:
                            # Update existing record
                            existing_id = existing_settlement_lookup[settlement_key]['id']
                            settlement_data['id'] = existing_id
                            updated_records.append(settlement_data)
                            records_updated += 1
                        else:
                            # New record
                            new_records.append(settlement_data)
                            records_added += 1
                
                except Exception as e:
                    logger.error(f"Error processing generation record: {e}")
                    records_skipped += 1
                    continue
            
            # Step 4: Process consumption records without matching generation
            logger.info("Processing consumption records without generation...")
            
            for idx, cons_record in consumption_df.iterrows():
                try:
                    client_name = cons_record['client_name']
                    cons_unit = cons_record['cons_unit']
                    
                    # Check if settlement already exists for this consumption
                    settlement_exists = False
                    for key in existing_settlement_lookup:
                        if (key[1] == client_name and key[2] == cons_unit and 
                            key[3] == cons_record['date'] and key[4] == cons_record['time']):
                            settlement_exists = True
                            break
                    
                    # Also check in new records
                    if not settlement_exists:
                        for new_record in new_records:
                            if (new_record['client_name'] == client_name and 
                                new_record['cons_unit'] == cons_unit and
                                new_record['date'] == cons_record['date'] and
                                new_record['time'] == cons_record['time']):
                                settlement_exists = True
                                break
                    
                    if not settlement_exists:
                        # Find mapping for this consumption unit
                        mapping_found = False
                        for mapping in mapping_lookup.get(client_name, []):
                            if mapping['cons_unit'] == cons_unit:
                                mapping_found = True
                                break
                        
                        if not mapping_found:
                            logger.warning(f"No mapping found for consumption unit: {cons_unit}")
                            records_skipped += 1
                            continue
                        
                        # Get plant info
                        plant_info = plant_lookup.get(client_name)
                        if plant_info:
                            plant_id, plant_type = plant_info
                            
                            consumption_val = cons_record['consumption'] if pd.notna(cons_record['consumption']) else 0
                            consumption = Decimal(consumption_val) if consumption_val is not None else Decimal(0)
                            
                            # When no generation is allocated, deficit equals consumption
                            deficit = consumption
                            surplus_demand = consumption
                            
                            settlement_data = {
                                'plant_id': plant_id,
                                'client_name': client_name,
                                'cons_unit': cons_unit,
                                'type': plant_type,
                                'date': cons_record['date'],
                                'time': cons_record['time'],
                                'allocated_generation': Decimal(0),
                                'consumption': consumption,
                                'deficit': deficit,
                                'surplus_demand': surplus_demand,
                                'surplus_generation': Decimal(0),
                                'settled': Decimal(0)
                            }
                            
                            new_records.append(settlement_data)
                            records_added += 1
                
                except Exception as e:
                    logger.error(f"Error processing consumption record: {e}")
                    records_skipped += 1
                    continue
            
            # Step 5: Batch insert/update operations
            logger.info("Performing batch database operations...")
            
            # Batch insert new records
            if new_records:
                logger.info(f"Inserting {len(new_records)} new records...")
                for i in range(0, len(new_records), self.batch_size):
                    batch = new_records[i:i + self.batch_size]
                    settlement_objects = [SettlementData(**record) for record in batch]
                    session.bulk_save_objects(settlement_objects)
                    session.commit()
                    logger.info(f"Inserted batch {i//self.batch_size + 1}/{(len(new_records)-1)//self.batch_size + 1}")
            
            # Batch update existing records
            if updated_records:
                logger.info(f"Updating {len(updated_records)} existing records...")
                for i in range(0, len(updated_records), self.batch_size):
                    batch = updated_records[i:i + self.batch_size]
                    session.bulk_update_mappings(SettlementData, batch)
                    session.commit()
                    logger.info(f"Updated batch {i//self.batch_size + 1}/{(len(updated_records)-1)//self.batch_size + 1}")
            
            session.close()
            
            execution_time = time.time() - start_time
            logger.info(f"✅ Settlement data processing completed in {execution_time:.2f} seconds:")
            logger.info(f"- {records_added} new records added")
            logger.info(f"- {records_updated} records updated")
            logger.info(f"- {records_skipped} records skipped")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error processing settlement data: {e}")
            import traceback
            traceback.print_exc()
            return False

    def recalculate_surplus_deficit(self, start_date=None, end_date=None, client_name=None):
        """
        Optimized recalculation of surplus/deficit for existing settlement data using bulk operations
        
        Args:
            start_date (str): Start date in 'YYYY-MM-DD' format (optional)
            end_date (str): End date in 'YYYY-MM-DD' format (optional)
            client_name (str): Filter by client name (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            start_time = time.time()
            logger.info("Starting optimized surplus/deficit recalculation")
            
            session = self.Session()
            
            # Build query
            query = session.query(SettlementData)
            
            if start_date:
                query = query.filter(SettlementData.date >= start_date)
            if end_date:
                query = query.filter(SettlementData.date <= end_date)
            if client_name:
                query = query.filter(SettlementData.client_name == client_name)
            
            # Load data into DataFrame for efficient processing
            settlement_df = pd.read_sql(query.statement, session.bind)
            logger.info(f"Loaded {len(settlement_df)} settlement records for recalculation")
            
            if len(settlement_df) == 0:
                logger.info("No records found to recalculate")
                session.close()
                return True
            
            # Perform vectorized calculations
            settlement_df['allocated_generation'] = settlement_df['allocated_generation'].fillna(0)
            settlement_df['consumption'] = settlement_df['consumption'].fillna(0)
            
            # Convert to Decimal for precise calculations
            settlement_df['allocated_generation'] = settlement_df['allocated_generation'].apply(Decimal)
            settlement_df['consumption'] = settlement_df['consumption'].apply(Decimal)
            
            # Calculate all settlement values vectorized
            settlement_df['deficit'] = settlement_df['allocated_generation'] - settlement_df['consumption']
            settlement_df['surplus_demand'] = settlement_df['deficit'].apply(lambda x: max(Decimal(0), -x))
            settlement_df['surplus_generation'] = settlement_df['deficit'].apply(lambda x: max(Decimal(0), x))
            settlement_df['settled'] = settlement_df.apply(lambda row: min(row['allocated_generation'], row['consumption']), axis=1)
            
            # Prepare update data
            update_records = []
            for _, record in settlement_df.iterrows():
                update_records.append({
                    'id': record['id'],
                    'deficit': record['deficit'],
                    'surplus_demand': record['surplus_demand'],
                    'surplus_generation': record['surplus_generation'],
                    'settled': record['settled']
                })
            
            # Batch update records
            updated_count = 0
            for i in range(0, len(update_records), self.batch_size):
                batch = update_records[i:i + self.batch_size]
                session.bulk_update_mappings(SettlementData, batch)
                session.commit()
                updated_count += len(batch)
                logger.info(f"Updated batch {i//self.batch_size + 1}/{(len(update_records)-1)//self.batch_size + 1}")
            
            session.close()
            
            execution_time = time.time() - start_time
            logger.info(f"✅ Updated surplus/deficit for {updated_count} records in {execution_time:.2f} seconds")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error recalculating surplus/deficit: {e}")
            import traceback
            traceback.print_exc()
            return False

    def recalculate_generation_allocation(self, start_date=None, end_date=None, client_name=None):
        """
        Recalculate generation allocation based on consumption_mapping percentages
        
        Args:
            start_date (str): Start date in 'YYYY-MM-DD' format (optional)
            end_date (str): End date in 'YYYY-MM-DD' format (optional)
            client_name (str): Filter by client name (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Recalculating generation allocation based on mapping percentages")
            
            session = self.Session()
            
            # Build generation query
            gen_query = select(TblGeneration)
            if start_date:
                gen_query = gen_query.where(TblGeneration.date >= start_date)
            if end_date:
                gen_query = gen_query.where(TblGeneration.date <= end_date)
            if client_name:
                gen_query = gen_query.where(TblGeneration.client_name == client_name)
            
            # Execute generation query
            generation_records = session.execute(gen_query).scalars().all()
            logger.info(f"Found {len(generation_records)} generation records to reallocate")
            
            records_updated = 0
            
            for gen_record in generation_records:
                try:
                    # Get consumption mapping for this client
                    mapping_query = select(ConsumptionMapping).where(
                        ConsumptionMapping.client_name == gen_record.client_name
                    )
                    
                    mappings = session.execute(mapping_query).scalars().all()
                    
                    if not mappings:
                        logger.warning(f"No consumption mapping found for client: {gen_record.client_name}")
                        continue
                    
                    # Process each mapping to allocate generation
                    for mapping in mappings:
                        # Calculate allocated generation based on percentage
                        if gen_record.supplied is not None:
                            allocated_generation = Decimal(gen_record.supplied) * (Decimal(mapping.percentage) / Decimal(100))
                        else:
                            allocated_generation = Decimal(0)
                        
                        # Find settlement record
                        settlement = session.query(SettlementData).filter_by(
                            plant_id=gen_record.plant_id,
                            client_name=gen_record.client_name,
                            cons_unit=mapping.cons_unit,
                            date=gen_record.date,
                            time=gen_record.time,
                            type=gen_record.type
                        ).first()
                        
                        if settlement:
                            # Update generation allocation
                            settlement.allocated_generation = allocated_generation
                            
                            # Recalculate all settlement values
                            consumption = Decimal(settlement.consumption) if settlement.consumption is not None else Decimal(0)
                            deficit = allocated_generation - consumption
                            surplus_demand = max(Decimal(0), -deficit)
                            surplus_generation = max(Decimal(0), allocated_generation - consumption)
                            settled = min(allocated_generation, consumption)


                            
                            # Update all fields
                            settlement.deficit = deficit
                            settlement.surplus_demand = surplus_demand
                            settlement.surplus_generation = surplus_generation
                            settlement.settled = settled
                            
                            records_updated += 1
                
                except Exception as e:
                    logger.error(f"Error recalculating allocation for generation record: {e}")
                    continue
            
            # Commit changes
            session.commit()
            session.close()
            
            logger.info(f"✅ Updated generation allocation for {records_updated} records")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error recalculating generation allocation: {e}")
            import traceback
            traceback.print_exc()
            return False

# Example usage
def main():
    # Initialize the data inserter
    inserter = SettlementDataInserter()
    
    # Process settlement data for all records
    inserter.process_settlement_data()
    
    

if __name__ == "__main__":
    main()





# import pandas as pd
# from sqlalchemy import create_engine, select, func
# from sqlalchemy.orm import sessionmaker
# from models import TblGeneration, TblConsumption, ConsumptionMapping, SettlementData, Base
# from datetime import datetime, timedelta
# import logging
# from decimal import Decimal

# # Configure logging
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
# )
# logger = logging.getLogger(__name__)

# class SettlementDataInserter:
#     def __init__(self, database_url="mysql+pymysql://root:test123@localhost/energy_db"):
#         """
#         Initialize the Settlement Data Inserter
        
#         Args:
#             database_url (str): Database connection URL
#         """
#         self.engine = create_engine(database_url)
#         self.Session = sessionmaker(bind=self.engine)
        
#     def process_settlement_data(self):
#         """
#         Process and insert settlement data from tbl_generation and tbl_consumption
#         using consumption_mapping to allocate generation
        
#         Args:
#             start_date (str): Start date in 'YYYY-MM-DD' format (optional)
#             end_date (str): End date in 'YYYY-MM-DD' format (optional)
#             client_name (str): Filter by client name (optional)
            
#         Returns:
#             bool: True if successful, False otherwise
#         """
#         try:
#             logger.info(f"Starting settlement data processing")
            
            
#             session = self.Session()
            
#             # Build generation query
#             gen_query = select(TblGeneration)
            
            
#             # Execute generation query
#             generation_records = session.execute(gen_query).scalars().all()
#             logger.info(f"Found {len(generation_records)} generation records")
            
#             # Process each generation record
#             records_added = 0
#             records_updated = 0
#             records_skipped = 0
            
#             for gen_record in generation_records:
#                 try:
#                     # Get consumption mapping for this client
#                     mapping_query = select(ConsumptionMapping).where(
#                         ConsumptionMapping.client_name == gen_record.client_name
#                     )
                    
#                     mappings = session.execute(mapping_query).scalars().all()
                    
#                     if not mappings:
#                         logger.warning(f"No consumption mapping found for client: {gen_record.client_name}")
#                         records_skipped += 1
#                         continue
                    
#                     # Process each mapping to allocate generation
#                     for mapping in mappings:
#                         # Calculate allocated generation based on percentage
#                         if gen_record.generation is not None:
#                             allocated_generation = Decimal(gen_record.generation) * (Decimal(mapping.percentage) / Decimal(100))
#                         else:
#                             allocated_generation = Decimal(0)
                        
#                         # Find matching consumption record
#                         cons_query = select(TblConsumption).where(
#                             TblConsumption.client_name == gen_record.client_name,
#                             TblConsumption.cons_unit == mapping.cons_unit,
#                             TblConsumption.date == gen_record.date,
#                             TblConsumption.time == gen_record.time
#                         )
                        
#                         cons_record = session.execute(cons_query).scalars().first()
                        
#                         # Set consumption value (0 if no matching record found)
#                         consumption = Decimal(cons_record.consumption) if cons_record and cons_record.consumption is not None else Decimal(0)
                        
#                         # Calculate deficit and surplus
#                         deficit = allocated_generation - consumption  # Positive when generation > consumption, negative when consumption > generation
#                         surplus_demand = max(Decimal(0), -deficit)    # Demand that wasn't met by allocated generation (when deficit is negative)
#                         surplus_generation = max(Decimal(0), allocated_generation - consumption)  # Generation that exceeds consumption
                        
#                         # Check if settlement record already exists
#                         existing = session.query(SettlementData).filter_by(
#                             plant_id=gen_record.plant_id,
#                             client_name=gen_record.client_name,
#                             cons_unit=mapping.cons_unit,
#                             date=gen_record.date,
#                             time=gen_record.time,
#                             type=gen_record.type
#                         ).first()
                        
#                         if existing:
#                             # Update existing record
#                             existing.allocated_generation = allocated_generation
#                             existing.consumption = consumption
#                             existing.deficit = deficit
#                             existing.surplus_demand = surplus_demand
#                             existing.surplus_generation = surplus_generation
#                             existing.settled = min(allocated_generation, consumption)
#                             records_updated += 1
#                         else:
#                             # Create new record
#                             settled = min(allocated_generation, consumption)
#                             new_record = SettlementData(
#                                 plant_id=gen_record.plant_id,
#                                 client_name=gen_record.client_name,
#                                 cons_unit=mapping.cons_unit,
#                                 type=gen_record.type,
#                                 date=gen_record.date,
#                                 time=gen_record.time,
#                                 allocated_generation=allocated_generation,
#                                 consumption=consumption,
#                                 deficit=deficit,
#                                 surplus_demand=surplus_demand,
#                                 surplus_generation=surplus_generation,
#                                 settled=settled
#                             )
#                             session.add(new_record)
#                             records_added += 1
                
#                 except Exception as e:
#                     logger.error(f"Error processing generation record: {e}")
#                     records_skipped += 1
#                     continue
            
#             # Process consumption records that don't have matching generation records
#             cons_query = select(TblConsumption)
            
            
#             consumption_records = session.execute(cons_query).scalars().all()
#             logger.info(f"Found {len(consumption_records)} consumption records")
            
#             for cons_record in consumption_records:
#                 try:
#                     # Check if there's already a settlement record for this consumption
#                     existing_settlement = session.query(SettlementData).filter(
#                         SettlementData.client_name == cons_record.client_name,
#                         SettlementData.cons_unit == cons_record.cons_unit,
#                         SettlementData.date == cons_record.date,
#                         SettlementData.time == cons_record.time
#                     ).first()
                    
#                     if not existing_settlement:
#                         # Find a mapping for this consumption unit
#                         mapping_query = select(ConsumptionMapping).where(
#                             ConsumptionMapping.client_name == cons_record.client_name,
#                             ConsumptionMapping.cons_unit == cons_record.cons_unit
#                         )
                        
#                         mapping = session.execute(mapping_query).scalars().first()
                        
#                         if not mapping:
#                             logger.warning(f"No mapping found for consumption unit: {cons_record.cons_unit}")
#                             records_skipped += 1
#                             continue
                        
#                         # Find a matching plant_id from generation records for this client
#                         plant_query = select(TblGeneration.plant_id, TblGeneration.type).where(
#                             TblGeneration.client_name == cons_record.client_name
#                         ).limit(1)
                        
#                         plant_result = session.execute(plant_query).first()
                        
#                         if plant_result:
#                             plant_id, plant_type = plant_result
                            
#                             # Create new settlement record with only consumption data
#                             consumption = Decimal(cons_record.consumption) if cons_record.consumption is not None else Decimal(0)
#                             # When no generation is allocated, deficit equals consumption, surplus_demand equals consumption
#                             deficit = consumption  # consumption - 0 (allocated_generation)
#                             surplus_demand = consumption  # All consumption is unmet demand
#                             new_record = SettlementData(
#                                 plant_id=plant_id,
#                                 client_name=cons_record.client_name,
#                                 cons_unit=cons_record.cons_unit,
#                                 type=plant_type,
#                                 date=cons_record.date,
#                                 time=cons_record.time,
#                                 allocated_generation=Decimal(0),
#                                 consumption=consumption,
#                                 deficit=deficit,
#                                 surplus_demand=surplus_demand,
#                                 surplus_generation=Decimal(0),
#                                 settled=Decimal(0)
#                             )
#                             session.add(new_record)
#                             records_added += 1
                
#                 except Exception as e:
#                     logger.error(f"Error processing consumption record: {e}")
#                     records_skipped += 1
#                     continue
            
#             # Commit changes
#             session.commit()
#             session.close()
            
#             logger.info(f"✅ Settlement data processing completed:")
#             logger.info(f"- {records_added} new records added")
#             logger.info(f"- {records_updated} records updated")
#             logger.info(f"- {records_skipped} records skipped")
            
#             return True
            
#         except Exception as e:
#             logger.error(f"❌ Error processing settlement data: {e}")
#             import traceback
#             traceback.print_exc()
#             return False

#     def recalculate_surplus_deficit(self, start_date=None, end_date=None, client_name=None):
#         """
#         Recalculate surplus/deficit for existing settlement data
        
#         Args:
#             start_date (str): Start date in 'YYYY-MM-DD' format (optional)
#             end_date (str): End date in 'YYYY-MM-DD' format (optional)
#             client_name (str): Filter by client name (optional)
            
#         Returns:
#             bool: True if successful, False otherwise
#         """
#         try:
#             logger.info(f"Recalculating surplus/deficit for settlement data")
            
#             session = self.Session()
            
#             # Build query
#             query = session.query(SettlementData)
            
#             if start_date:
#                 query = query.filter(SettlementData.date >= start_date)
#             if end_date:
#                 query = query.filter(SettlementData.date <= end_date)
#             if client_name:
#                 query = query.filter(SettlementData.client_name == client_name)
            
#             records = query.all()
#             updated_count = 0
            
#             for record in records:
#                 allocated_generation = Decimal(record.allocated_generation) if record.allocated_generation is not None else Decimal(0)
#                 consumption = Decimal(record.consumption) if record.consumption is not None else Decimal(0)
                
#                 # Calculate all settlement values
#                 deficit = allocated_generation - consumption
#                 surplus_demand = max(Decimal(0), -deficit)
#                 surplus_generation = max(Decimal(0), allocated_generation - consumption)
#                 settled = min(allocated_generation, consumption)
                
#                 # Update record
#                 record.deficit = deficit
#                 record.surplus_demand = surplus_demand
#                 record.surplus_generation = surplus_generation
#                 record.settled = settled
                
#                 updated_count += 1
            
#             session.commit()
#             session.close()
            
#             logger.info(f"✅ Updated surplus/deficit for {updated_count} records")
#             return True
            
#         except Exception as e:
#             logger.error(f"❌ Error recalculating surplus/deficit: {e}")
#             import traceback
#             traceback.print_exc()
#             return False

#     def recalculate_generation_allocation(self, start_date=None, end_date=None, client_name=None):
#         """
#         Recalculate generation allocation based on consumption_mapping percentages
        
#         Args:
#             start_date (str): Start date in 'YYYY-MM-DD' format (optional)
#             end_date (str): End date in 'YYYY-MM-DD' format (optional)
#             client_name (str): Filter by client name (optional)
            
#         Returns:
#             bool: True if successful, False otherwise
#         """
#         try:
#             logger.info(f"Recalculating generation allocation based on mapping percentages")
            
#             session = self.Session()
            
#             # Build generation query
#             gen_query = select(TblGeneration)
#             if start_date:
#                 gen_query = gen_query.where(TblGeneration.date >= start_date)
#             if end_date:
#                 gen_query = gen_query.where(TblGeneration.date <= end_date)
#             if client_name:
#                 gen_query = gen_query.where(TblGeneration.client_name == client_name)
            
#             # Execute generation query
#             generation_records = session.execute(gen_query).scalars().all()
#             logger.info(f"Found {len(generation_records)} generation records to reallocate")
            
#             records_updated = 0
            
#             for gen_record in generation_records:
#                 try:
#                     # Get consumption mapping for this client
#                     mapping_query = select(ConsumptionMapping).where(
#                         ConsumptionMapping.client_name == gen_record.client_name
#                     )
                    
#                     mappings = session.execute(mapping_query).scalars().all()
                    
#                     if not mappings:
#                         logger.warning(f"No consumption mapping found for client: {gen_record.client_name}")
#                         continue
                    
#                     # Process each mapping to allocate generation
#                     for mapping in mappings:
#                         # Calculate allocated generation based on percentage
#                         if gen_record.generation is not None:
#                             allocated_generation = Decimal(gen_record.generation) * (Decimal(mapping.percentage) / Decimal(100))
#                         else:
#                             allocated_generation = Decimal(0)
                        
#                         # Find settlement record
#                         settlement = session.query(SettlementData).filter_by(
#                             plant_id=gen_record.plant_id,
#                             client_name=gen_record.client_name,
#                             cons_unit=mapping.cons_unit,
#                             date=gen_record.date,
#                             time=gen_record.time,
#                             type=gen_record.type
#                         ).first()
                        
#                         if settlement:
#                             # Update generation allocation
#                             settlement.allocated_generation = allocated_generation
                            
#                             # Recalculate all settlement values
#                             consumption = Decimal(settlement.consumption) if settlement.consumption is not None else Decimal(0)
#                             deficit = allocated_generation - consumption
#                             surplus_demand = max(Decimal(0), -deficit)
#                             surplus_generation = max(Decimal(0), allocated_generation - consumption)
#                             settled = min(allocated_generation, consumption)


                            
#                             # Update all fields
#                             settlement.deficit = deficit
#                             settlement.surplus_demand = surplus_demand
#                             settlement.surplus_generation = surplus_generation
#                             settlement.settled = settled
                            
#                             records_updated += 1
                
#                 except Exception as e:
#                     logger.error(f"Error recalculating allocation for generation record: {e}")
#                     continue
            
#             # Commit changes
#             session.commit()
#             session.close()
            
#             logger.info(f"✅ Updated generation allocation for {records_updated} records")
#             return True
            
#         except Exception as e:
#             logger.error(f"❌ Error recalculating generation allocation: {e}")
#             import traceback
#             traceback.print_exc()
#             return False

# # Example usage
# def main():
#     # Initialize the data inserter
#     inserter = SettlementDataInserter()
    
#     # Process settlement data for all records
#     inserter.process_settlement_data()
    
    

# if __name__ == "__main__":
#     main()