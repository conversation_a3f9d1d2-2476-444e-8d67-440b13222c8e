import pandas as pd
import numpy as np
from datetime import datetime
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine
from models import TblGeneration
from db_setup import engine, SessionLocal
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_generation_data_from_csv():
    """
    Read data from KIDS June generation.csv and insert into tbl_generation table
    """
    try:
        # Read the CSV file
        csv_file_path = "KIDS June generation.csv"
        logger.info(f"Reading CSV file: {csv_file_path}")
        
        df = pd.read_csv(csv_file_path)
        logger.info(f"Loaded {len(df)} rows from CSV")
        logger.info(f"CSV Columns: {list(df.columns)}")
        
        # Check for required columns
        required_columns = ['Date & Time', 'plant_id', 'plant_name', 'client_name', 'type', 'Total_Generation']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logger.error(f"Missing required columns in CSV: {missing_columns}")
            return
        
        # Create session
        session = SessionLocal()
        
        # Process each row
        inserted_count = 0
        skipped_count = 0
        
        for index, row in df.iterrows():
            try:
                # Parse the datetime - skip only if datetime is NaN or invalid
                datetime_str = row.get('Date & Time', None)
                if pd.isna(datetime_str) or not isinstance(datetime_str, str) or not datetime_str.strip():
                    logger.debug(f"Skipping row {index} due to missing or empty datetime: {datetime_str}")
                    skipped_count += 1
                    continue
                
                # Fix malformed datetime format - remove Z if timezone offset is present
                if 'Z+' in datetime_str or 'Z-' in datetime_str:
                    datetime_str = datetime_str.replace('Z', '')

                # Try robust date parsing - handle ISO format with timezone
                try:
                    dt = pd.to_datetime(datetime_str, errors='coerce')
                    if pd.isna(dt):
                        logger.debug(f"Skipping row {index} due to invalid datetime format: {datetime_str}")
                        skipped_count += 1
                        continue

                    # If datetime is timezone-aware, convert to IST and make timezone-naive
                    if dt.tz is not None:
                        dt = dt.tz_convert('Asia/Kolkata').tz_localize(None)

                except Exception as e:
                    logger.debug(f"Skipping row {index} due to datetime parsing error: {datetime_str}, error: {e}")
                    skipped_count += 1
                    continue
                
                # Handle NaN values in fields - convert to appropriate defaults
                plant_id = row.get('plant_id', 'UNKNOWN')
                if pd.isna(plant_id):
                    plant_id = 'UNKNOWN'
                plant_name = row.get('plant_name', 'Unknown Plant')
                if pd.isna(plant_name):
                    plant_name = 'Unknown Plant'
                client_name = row.get('client_name', 'Unknown Client')
                if pd.isna(client_name):
                    client_name = 'Unknown Client'
                type_value = row.get('type', 'unknown')
                if pd.isna(type_value):
                    type_value = 'unknown'
                
                # Handle NaN values in Total_Generation - set to 0
                total_gen = row.get('Total_Generation', 0.0)
                if pd.isna(total_gen):
                    total_gen = 0.0
                else:
                    try:
                        total_gen = float(total_gen)
                    except Exception:
                        total_gen = 0.0
                
                # Handle new columns - pr, poa, avg_wind_speed
                pr_value = None
                if 'Average_PR' in df.columns:
                    pr_raw = row.get('Average_PR', None)
                    if not pd.isna(pr_raw):
                        try:
                            pr_value = float(pr_raw)
                        except Exception:
                            pr_value = None
                
                poa_value = None
                if 'POA' in df.columns:
                    poa_raw = row.get('POA', None)
                    if not pd.isna(poa_raw):
                        try:
                            poa_value = float(poa_raw)
                        except Exception:
                            poa_value = None
                
                avg_wind_speed_value = None
                if 'Avg_Wind_Speed' in df.columns:
                    wind_raw = row.get('Avg_Wind_Speed', None)
                    if not pd.isna(wind_raw):
                        try:
                            avg_wind_speed_value = float(wind_raw)
                        except Exception:
                            avg_wind_speed_value = None
                
                # Check if record already exists (based on unique constraint)
                existing_record = session.query(TblGeneration).filter_by(
                    plant_id=plant_id,
                    date=dt.date(),
                    time=dt.time(),
                    type=type_value
                ).first()
                
                if existing_record:
                    logger.debug(f"Record already exists for plant_id={plant_id}, date={dt.date()}, time={dt.time()}, type={type_value}, skipping...")
                    skipped_count += 1
                    continue
                
                # Create new generation record
                generation_record = TblGeneration(
                    plant_id=plant_id,
                    plant_name=plant_name,
                    client_name=client_name,
                    type=type_value,
                    date=dt.date(),
                    time=dt.time(),
                    generation=total_gen,
                    active_power=None,  # Set to None as it's not provided in CSV
                    pr=pr_value,
                    poa=poa_value,
                    avg_wind_speed=avg_wind_speed_value
                )
                
                session.add(generation_record)
                inserted_count += 1
                
                # Commit every 100 records for better performance
                if inserted_count % 100 == 0:
                    session.commit()
                    logger.info(f"Committed {inserted_count} records...")
                    
            except Exception as e:
                logger.error(f"Error processing row {index}: {e}")
                session.rollback()
                skipped_count += 1
                continue
        
        # Final commit
        session.commit()
        logger.info(f"Data insertion completed successfully!")
        logger.info(f"Total records inserted: {inserted_count}")
        logger.info(f"Total records skipped (duplicates, missing, and datetime issues): {skipped_count}")
        
    except Exception as e:
        logger.error(f"Error in add_generation_data_from_csv: {e}")
        if 'session' in locals():
            session.rollback()
        raise
    finally:
        if 'session' in locals():
            session.close()

def verify_inserted_data():
    """
    Verify the inserted data by checking a few records
    """
    try:
        session = SessionLocal()
        
        # Get count of records for this plant
        count = session.query(TblGeneration).filter_by(
            plant_id='IN.INTE.KIDS',
            type='solar'
        ).count()
        
        logger.info(f"Total records found for plant IN.INTE.KIDS: {count}")
        
        # Get first few records
        first_records = session.query(TblGeneration).filter_by(
            plant_id='IN.INTE.KIDS',
            type='solar'
        ).limit(5).all()
        
        logger.info("First 5 records:")
        for record in first_records:
            logger.info(f"Date: {record.date}, Time: {record.time}, Generation: {record.generation}, "
                       f"PR: {record.pr}, POA: {record.poa}, Avg Wind Speed: {record.avg_wind_speed}")
            
    except Exception as e:
        logger.error(f"Error in verify_inserted_data: {e}")
    finally:
        if 'session' in locals():
            session.close()

if __name__ == "__main__":
    logger.info("Starting generation data insertion...")
    
    # Add the data
    add_generation_data_from_csv()
    
    # Verify the insertion
    logger.info("Verifying inserted data...")
    verify_inserted_data()
    
    logger.info("Process completed!")