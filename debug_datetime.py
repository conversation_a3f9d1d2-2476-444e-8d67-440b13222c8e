import pandas as pd
from datetime import datetime

# Test the datetime parsing with the actual format from CSV
test_datetime = "2025-06-01T00:00:00Z+05:30"
print(f"Original datetime string: {test_datetime}")

# Try different parsing methods
try:
    # Method 1: pandas to_datetime with utc=True
    dt1 = pd.to_datetime(test_datetime, errors='coerce', utc=True)
    print(f"Method 1 (pd.to_datetime with utc=True): {dt1}")
    if not pd.isna(dt1):
        dt1_local = dt1.tz_convert('Asia/Kolkata').tz_localize(None)
        print(f"Method 1 converted to IST: {dt1_local}")
        print(f"Method 1 date: {dt1_local.date()}")
        print(f"Method 1 time: {dt1_local.time()}")
except Exception as e:
    print(f"Method 1 failed: {e}")

try:
    # Method 2: pandas to_datetime without utc
    dt2 = pd.to_datetime(test_datetime, errors='coerce')
    print(f"Method 2 (pd.to_datetime without utc): {dt2}")
    if not pd.isna(dt2):
        print(f"Method 2 date: {dt2.date()}")
        print(f"Method 2 time: {dt2.time()}")
except Exception as e:
    print(f"Method 2 failed: {e}")

try:
    # Method 3: Direct datetime parsing
    # Remove the Z and handle timezone manually
    clean_datetime = test_datetime.replace('Z', '')
    dt3 = pd.to_datetime(clean_datetime, errors='coerce')
    print(f"Method 3 (cleaned string): {dt3}")
    if not pd.isna(dt3):
        print(f"Method 3 date: {dt3.date()}")
        print(f"Method 3 time: {dt3.time()}")
except Exception as e:
    print(f"Method 3 failed: {e}")

# Test with a few more samples from the CSV
print("\n" + "="*50)
print("Testing with CSV data:")

# Read first few rows
df = pd.read_csv("KIDS June generation.csv")
print(f"CSV columns: {list(df.columns)}")
print(f"First 3 datetime values:")
for i in range(3):
    dt_str = df.iloc[i]['Date & Time']
    print(f"Row {i}: {dt_str}")
    
    # Test parsing
    dt_parsed = pd.to_datetime(dt_str, errors='coerce')
    print(f"  Parsed: {dt_parsed}")
    if not pd.isna(dt_parsed):
        print(f"  Date: {dt_parsed.date()}, Time: {dt_parsed.time()}")
    print()