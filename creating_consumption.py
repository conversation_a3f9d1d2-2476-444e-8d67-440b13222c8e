# import pandas as pd
# from datetime import timedelta
# import os

# def expand_hourly_to_15min(input_path: str, output_path: str = None):
#     # Step 1: Read input file
#     if input_path.endswith('.csv'):
#         df = pd.read_csv(input_path)
#     elif input_path.endswith(('.xlsx', '.xls')):
#         df = pd.read_excel(input_path)
#     else:
#         raise ValueError("Unsupported file format. Use CSV or Excel.")

#     # Step 2: Standardize column names
#     df.columns = [col.strip().upper().replace('|', '').replace(' ', '_') for col in df.columns]
#     print("✅ Columns detected:", df.columns.tolist())

#     # Step 3: Identify necessary columns
#     datetime_col = next((col for col in df.columns if 'DATETIME' in col), None)
#     energy_col = next((col for col in df.columns if 'P15' in col and 'WHRS' in col), None)

#     if not datetime_col or not energy_col:
#         raise ValueError("❌ Required columns 'DATETIME' and 'P15_MFM_WHRS' not found.")

#     # Step 4: Rename for consistency
#     df.rename(columns={datetime_col: 'DATETIME', energy_col: 'P15_MFM_WHRS'}, inplace=True)

#     # Step 5: Fix and normalize datetime
#     df['DATETIME'] = pd.to_datetime(df['DATETIME'], errors='coerce')
#     if df['DATETIME'].isnull().any():
#         raise ValueError("❌ Some DATETIME values couldn't be parsed. Check input format.")

#     df['DATETIME'] = df['DATETIME'].dt.floor('min')  # Truncate to nearest minute
#     df = df.sort_values('DATETIME').reset_index(drop=True)

#     # Step 6: Expand hourly rows into 15-min slots
#     expanded_rows = []
#     for _, row in df.iterrows():
#         start_time = row['DATETIME']
#         energy = row['P15_MFM_WHRS']
#         energy_per_slot = round(energy / 4, 4) if pd.notnull(energy) else 0.0

#         for i in range(4):
#             new_time = start_time + timedelta(minutes=15 * i)
#             expanded_rows.append({
#                 'DATETIME': new_time,
#                 'P15_MFM_WHRS': energy_per_slot
#             })

#     expanded_df = pd.DataFrame(expanded_rows)

#     # Step 7: Save output
#     if not output_path:
#         base, ext = os.path.splitext(input_path)
#         output_path = base + '_15min_new.csv'

#     expanded_df.to_csv(output_path, index=False)
#     print(f"✅ Successfully saved 15-min expanded data to: {output_path}")

# # === Run Script ===
# if __name__ == "__main__":
#     input_file = "energy_data_preprocessed.xlsx"  # Replace with your file
#     expand_hourly_to_15min(input_file)





# import pandas as pd

# # Load the existing CSV
# file_path = "raw consumption  data hourly.xlsx"  # Replace with your actual file path
# df = pd.read_excel(file_path)

# # Add or update the 'R.R.No' column with the desired value
# df['R.R.No'] = "E8HT-203"

# # Save the modified DataFrame back to CSV (overwrite or create a new file)
# df.to_csv("Raw consumption Hourly.csv", index=False)

# print("R.R.No column updated successfully.")






# import pandas as pd
# import numpy as np

# # Step 1: Load HRBR base data
# df = pd.read_csv("Raw consumption Hourly.csv", parse_dates=["time"], dayfirst=True)

# # Step 2: Define share percentages per Division (from your data)
# share_percentages = {
#     "Malleswaram": 8.96,
#     "ELECTRIONIC CITY": 11.15,
#     "KANAKAPURA": 8.08,
#     "BELLANDUR": 7.49,
#     "SARJAPURA": 8.45,
#     "SAHAKAR NAGAR": 9.66,
#     "HRBR UNIT": 7.62,  # base unit
#     "WHITEFIELD": 11.69,
#     "BELLANDUR CORP. OFFICE": 4.54,
#     "THANISANDRA": 9.15,
#     "Old Airport Road": 13.22
# }

# # Step 3: Define R.R.No and map to Division
# units_metadata = [
#     {"R.R.No": "C2HT-136", "Division": "Malleswaram"},
#     {"R.R.No": "S13HT-87", "Division": "ELECTRIONIC CITY"},
#     {"R.R.No": "S12HT-99", "Division": "KANAKAPURA"},
#     {"R.R.No": "S11HT-124", "Division": "BELLANDUR"},
#     {"R.R.No": "S11HT-419", "Division": "SARJAPURA"},
#     {"R.R.No": "C8HT-111", "Division": "SAHAKAR NAGAR"},
#     {"R.R.No": "E8HT-203", "Division": "HRBR UNIT"},
#     {"R.R.No": "E4HT-355", "Division": "WHITEFIELD"},
#     {"R.R.No": "S11BHT 406", "Division": "BELLANDUR CORP. OFFICE"},
#     {"R.R.No": "C8HT-135", "Division": "THANISANDRA"},
#     {"R.R.No": "E6HT209", "Division": "Old Airport Road"},
# ]

# base_consumer = df["Consumer"].iloc[0]
# base_type = df["Type"].iloc[0]
# base_unit_share = share_percentages["HRBR UNIT"]

# all_units = []

# for unit in units_metadata:
#     division = unit["Division"]
#     rr_no = unit["R.R.No"]

#     temp_df = df.copy()
#     temp_df["R.R.No"] = rr_no
#     temp_df["Division"] = division
#     temp_df["Type"] = base_type
#     temp_df["Consumer"] = base_consumer

#     # Share-aware scaling
#     target_share = share_percentages[division]
#     scale = target_share / base_unit_share

#     # Small row-level noise for realism
#     noise = np.random.normal(loc=1.0, scale=0.02, size=len(temp_df))

#     temp_df["Energy_kWh"] = (df["Energy_kWh"] * scale * noise).round(3)

#     all_units.append(temp_df)

# # Combine all and export
# final_df = pd.concat(all_units, ignore_index=True)
# final_df.to_csv("scaled_consumption_11_units_hourly.csv", index=False)

# print("✅ Done! Saved as 'share_scaled_consumption_11_units.csv'")



import pandas as pd
import numpy as np

# Step 1: Load HRBR base data
df = pd.read_csv("Raw consumption Hourly.csv", parse_dates=["time"], dayfirst=True)

# Step 2: Define share percentages per Division
share_percentages = {
    "Malleswaram": 8.96,
    "ELECTRIONIC CITY": 11.15,
    "KANAKAPURA": 8.08,
    "BELLANDUR": 7.49,
    "SARJAPURA": 8.45,
    "SAHAKAR NAGAR": 9.66,
    "HRBR UNIT": 7.62,  # base unit
    "WHITEFIELD": 11.69,
    "BELLANDUR CORP. OFFICE": 4.54,
    "THANISANDRA": 9.15,
    "Old Airport Road": 13.22
}

# Step 3: Define R.R.No and map to Division
units_metadata = [
    {"R.R.No": "C2HT-136", "Division": "Malleswaram"},
    {"R.R.No": "S13HT-87", "Division": "ELECTRIONIC CITY"},
    {"R.R.No": "S12HT-99", "Division": "KANAKAPURA"},
    {"R.R.No": "S11HT-124", "Division": "BELLANDUR"},
    {"R.R.No": "S11HT-419", "Division": "SARJAPURA"},
    {"R.R.No": "C8HT-111", "Division": "SAHAKAR NAGAR"},
    {"R.R.No": "E8HT-203", "Division": "HRBR UNIT"},
    {"R.R.No": "E4HT-355", "Division": "WHITEFIELD"},
    {"R.R.No": "S11BHT 406", "Division": "BELLANDUR CORP. OFFICE"},
    {"R.R.No": "C8HT-135", "Division": "THANISANDRA"},
    {"R.R.No": "E6HT209", "Division": "Old Airport Road"},
]

# Step 4: Extract base metadata
base_consumer = df["Consumer"].iloc[0]
base_type = df["Type"].iloc[0]
base_unit_share = share_percentages["HRBR UNIT"]

# Step 5: Generate scaled data
all_units = []
for unit in units_metadata:
    division = unit["Division"]
    rr_no = unit["R.R.No"]

    temp_df = df.copy()
    temp_df["R.R.No"] = rr_no
    temp_df["Division"] = division
    temp_df["Type"] = base_type
    temp_df["Consumer"] = base_consumer

    target_share = share_percentages[division]
    scale = target_share / base_unit_share

    noise = np.random.normal(loc=1.0, scale=0.02, size=len(temp_df))
    temp_df["Energy_kWh"] = (df["Energy_kWh"] * scale * noise).round(3)

    all_units.append(temp_df)

# Step 6: Combine and pivot
final_df = pd.concat(all_units, ignore_index=True)

pivot_df = final_df.pivot_table(
    index="time",
    columns="Division",
    values="Energy_kWh",
    aggfunc="sum"
).reset_index()

# Step 7: Save final CSV with original filename
pivot_df.to_csv("scaled_consumption_11_units_hourly.csv", index=False)

print("✅ Done! Saved as 'scaled_consumption_11_units_hourly.csv' with Division-wise columns.")
